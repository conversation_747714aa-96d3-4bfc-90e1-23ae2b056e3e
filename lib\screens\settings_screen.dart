import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/models/embedding_model_config.dart';
import 'package:novel_app/screens/network_test_screen.dart';
import 'package:novel_app/widgets/themed_dropdown.dart';
import 'package:novel_app/utils/network_client.dart';
import 'package:novel_app/utils/gemini_test.dart';
import 'package:novel_app/utils/openai_proxy_test.dart';
import 'package:novel_app/utils/ssl_fix_helper.dart';
import 'package:novel_app/services/ai_service.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final ApiConfigController controller = Get.find<ApiConfigController>();
  final NovelController novelController = Get.find<NovelController>();

  // 文本编辑控制器
  final Map<String, TextEditingController> _textControllers = {};
  Worker? _modelChangeWorker;

  @override
  void initState() {
    super.initState();
    // 初始化控制器
    _initTextControllers();
  }

  @override
  void dispose() {
    // 取消监听器
    _modelChangeWorker?.dispose();
    // 释放所有控制器
    for (var controller in _textControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  // 初始化文本控制器
  void _initTextControllers() {
    final currentModel = controller.getCurrentModel();

    // 为每个模型字段创建控制器
    _textControllers['apiKey'] =
        TextEditingController(text: currentModel.apiKey);
    _textControllers['apiUrl'] =
        TextEditingController(text: currentModel.apiUrl);
    _textControllers['apiPath'] =
        TextEditingController(text: currentModel.apiPath);
    _textControllers['model'] = TextEditingController(text: currentModel.model);
    _textControllers['appId'] = TextEditingController(text: currentModel.appId);
    _textControllers['maxTokens'] =
        TextEditingController(text: currentModel.maxTokens.toString());
    _textControllers['proxyUrl'] =
        TextEditingController(text: currentModel.proxyUrl);
    _textControllers['timeout'] =
        TextEditingController(text: currentModel.timeout.toString());

    // 嵌入模型控制器
    _textControllers['embeddingApiKey'] =
        TextEditingController(text: controller.embeddingModel.value.apiKey);
    _textControllers['embeddingBaseUrl'] =
        TextEditingController(text: controller.embeddingModel.value.baseUrl);
    _textControllers['embeddingApiPath'] =
        TextEditingController(text: controller.embeddingModel.value.apiPath);
    _textControllers['embeddingModelName'] =
        TextEditingController(text: controller.embeddingModel.value.modelName);
    _textControllers['embeddingTopK'] = TextEditingController(
        text: controller.embeddingModel.value.topK.toString());
    _textControllers['embeddingProxyUrl'] =
        TextEditingController(text: controller.embeddingModel.value.proxyUrl);
    _textControllers['embeddingTimeout'] = TextEditingController(
        text: controller.embeddingModel.value.timeout.toString());
  }

  // 更新文本控制器
  void _updateTextControllers() {
    if (!mounted) return;

    final currentModel = controller.getCurrentModel();

    // 更新模型字段控制器
    try {
      _textControllers['apiKey']?.text = currentModel.apiKey;
      _textControllers['apiUrl']?.text = currentModel.apiUrl;
      _textControllers['apiPath']?.text = currentModel.apiPath;
      _textControllers['model']?.text = currentModel.model;
      _textControllers['appId']?.text = currentModel.appId;
      _textControllers['maxTokens']?.text = currentModel.maxTokens.toString();
      _textControllers['proxyUrl']?.text = currentModel.proxyUrl;
      _textControllers['timeout']?.text = currentModel.timeout.toString();
    } catch (e) {
      // 如果控制器已被释放，忽略错误
      print('更新文本控制器时出错: $e');
    }
  }

  // 检查代理状态
  Future<void> _checkProxyStatus() async {
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在检查代理状态...'),
            ],
          ),
        ),
      );

      // 获取代理状态
      final proxyStatus = await NetworkClient.getProxyStatus();

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示结果对话框
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('代理状态检查结果'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildStatusItem(
                    '环境变量代理',
                    proxyStatus['hasEnvironmentProxy'] ? '已配置' : '未配置',
                    proxyStatus['hasEnvironmentProxy'],
                  ),
                  if (proxyStatus['environmentProxy'] != null)
                    Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Text(
                        '地址: ${proxyStatus['environmentProxy']}',
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ),
                  const SizedBox(height: 8),
                  _buildStatusItem(
                    '可用代理端口',
                    '${proxyStatus['availableProxyPorts'].length} 个',
                    proxyStatus['availableProxyPorts'].isNotEmpty,
                  ),
                  if (proxyStatus['availableProxyPorts'].isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Text(
                        '端口: ${proxyStatus['availableProxyPorts'].join(', ')}',
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ),
                  const SizedBox(height: 8),
                  if (proxyStatus['recommendedProxy'] != null) ...[
                    const Text(
                      '推荐配置:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              proxyStatus['recommendedProxy'],
                              style: const TextStyle(fontFamily: 'monospace'),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.copy, size: 16),
                            onPressed: () {
                              // 自动填入推荐的代理配置
                              _textControllers['proxyUrl']?.text =
                                  proxyStatus['recommendedProxy'];
                              controller.updateModelConfig(
                                controller.getCurrentModel().name,
                                proxyUrl: proxyStatus['recommendedProxy'],
                              );
                              Navigator.of(context).pop();
                              Get.snackbar('成功', '已自动填入推荐的代理配置');
                            },
                          ),
                        ],
                      ),
                    ),
                  ] else ...[
                    const Text(
                      '建议:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      '未检测到可用代理。如果您使用VPN，通常无需配置代理。如果需要代理，请确保代理软件正在运行。',
                      style: TextStyle(fontSize: 12, color: Colors.orange),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示错误信息
      if (mounted) {
        Get.snackbar('错误', '检查代理状态失败: $e');
      }
    }
  }

  Widget _buildStatusItem(String label, String value, bool isGood) {
    return Row(
      children: [
        Icon(
          isGood ? Icons.check_circle : Icons.cancel,
          color: isGood ? Colors.green : Colors.red,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text('$label: '),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: isGood ? Colors.green : Colors.red,
          ),
        ),
      ],
    );
  }

  // 简单测试Gemini连接
  Future<void> _testGeminiConnection() async {
    final currentModel = controller.getCurrentModel();

    if (currentModel.apiKey.isEmpty) {
      Get.snackbar('错误', '请先配置API密钥');
      return;
    }

    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在测试连接...'),
            ],
          ),
        ),
      );

      // 执行简单测试
      final result = await GeminiTest.testBasicConnection(
        apiKey: currentModel.apiKey,
        model: currentModel.model,
      );

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示结果
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(
              result['success'] ? '✅ 测试成功' : '❌ 测试失败',
              style: TextStyle(
                color: result['success'] ? Colors.green : Colors.red,
              ),
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('消息: ${result['message']}'),
                  if (result['response'] != null) ...[
                    const SizedBox(height: 8),
                    Text('响应: ${result['response']}'),
                  ],
                  if (result['duration'] != null) ...[
                    const SizedBox(height: 8),
                    Text('耗时: ${result['duration']}ms'),
                  ],
                  if (result['error'] != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      '错误详情: ${result['error']}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示错误信息
      if (mounted) {
        Get.snackbar('错误', '测试失败: $e');
      }
    }
  }

  // 使用Dio测试连接
  Future<void> _testWithDio() async {
    final currentModel = controller.getCurrentModel();

    if (currentModel.apiKey.isEmpty) {
      Get.snackbar('错误', '请先配置API密钥');
      return;
    }

    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在使用Dio测试连接...'),
            ],
          ),
        ),
      );

      // 执行Dio测试
      final result = await GeminiTest.testWithDio(
        apiKey: currentModel.apiKey,
        model: currentModel.model,
      );

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示结果
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(
              result['success'] ? '🚀 Dio测试成功' : '❌ Dio测试失败',
              style: TextStyle(
                color: result['success'] ? Colors.green : Colors.red,
              ),
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('消息: ${result['message']}'),
                  if (result['response'] != null) ...[
                    const SizedBox(height: 8),
                    Text('响应: ${result['response']}'),
                  ],
                  if (result['duration'] != null) ...[
                    const SizedBox(height: 8),
                    Text('耗时: ${result['duration']}ms'),
                  ],
                  if (result['error'] != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      '错误详情: ${result['error']}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示错误信息
      if (mounted) {
        Get.snackbar('错误', 'Dio测试失败: $e');
      }
    }
  }

  // 使用系统代理测试连接
  Future<void> _testWithSystemProxy() async {
    final currentModel = controller.getCurrentModel();

    if (currentModel.apiKey.isEmpty) {
      Get.snackbar('错误', '请先配置API密钥');
      return;
    }

    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在使用系统代理测试连接...'),
            ],
          ),
        ),
      );

      // 执行系统代理测试
      final result = await GeminiTest.testWithSystemProxy(
        apiKey: currentModel.apiKey,
        model: currentModel.model,
      );

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示结果
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(
              result['success'] ? '🌐 系统代理测试成功' : '❌ 系统代理测试失败',
              style: TextStyle(
                color: result['success'] ? Colors.green : Colors.red,
              ),
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('消息: ${result['message']}'),
                  if (result['response'] != null) ...[
                    const SizedBox(height: 8),
                    Text('响应: ${result['response']}'),
                  ],
                  if (result['duration'] != null) ...[
                    const SizedBox(height: 8),
                    Text('耗时: ${result['duration']}ms'),
                  ],
                  if (result['error'] != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      '错误详情: ${result['error']}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示错误信息
      if (mounted) {
        Get.snackbar('错误', '系统代理测试失败: $e');
      }
    }
  }

  // 测试OpenAI代理连接（SSL修复模式）
  Future<void> _testOpenAIProxy() async {
    final currentModel = controller.getCurrentModel();

    if (currentModel.apiKey.isEmpty) {
      Get.snackbar('错误', '请先配置API密钥');
      return;
    }

    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在测试OpenAI代理连接...'),
            ],
          ),
        ),
      );

      // 执行OpenAI代理测试
      final result = await OpenAIProxyTest.testConnection(
        baseUrl: currentModel.apiUrl,
        apiKey: currentModel.apiKey,
        model: currentModel.model,
        useTrustAllMode: false, // 先尝试正常模式
      );

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 如果正常模式失败且是SSL证书问题，尝试信任所有证书模式
      if (!result['success'] && result['canRetryWithTrustAll'] == true) {
        final retryResult = await _showSSLRetryDialog();
        if (retryResult == true) {
          await _testOpenAIProxyWithTrustAll();
          return;
        }
      }

      // 显示测试结果
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(
              result['success'] ? '✅ 连接成功' : '❌ 连接失败',
              style: TextStyle(
                color: result['success'] ? Colors.green : Colors.red,
              ),
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('状态: ${result['message']}'),
                  if (result['response'] != null) ...[
                    const SizedBox(height: 8),
                    Text('响应: ${result['response']}'),
                  ],
                  if (result['duration'] != null) ...[
                    const SizedBox(height: 8),
                    Text('耗时: ${result['duration']}ms'),
                  ],
                  if (result['suggestions'] != null) ...[
                    const SizedBox(height: 12),
                    const Text('建议:', style: TextStyle(fontWeight: FontWeight.bold)),
                    ...result['suggestions'].map<Widget>((suggestion) =>
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text('• $suggestion'),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示错误信息
      if (mounted) {
        Get.snackbar('错误', 'OpenAI代理测试失败: $e');
      }
    }
  }

  // 使用信任所有证书模式测试OpenAI代理
  Future<void> _testOpenAIProxyWithTrustAll() async {
    final currentModel = controller.getCurrentModel();

    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在使用SSL修复模式测试...'),
            ],
          ),
        ),
      );

      // 执行SSL修复模式测试
      final result = await OpenAIProxyTest.testConnection(
        baseUrl: currentModel.apiUrl,
        apiKey: currentModel.apiKey,
        model: currentModel.model,
        useTrustAllMode: true,
      );

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示测试结果
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(
              result['success'] ? '✅ SSL修复模式连接成功' : '❌ SSL修复模式连接失败',
              style: TextStyle(
                color: result['success'] ? Colors.green : Colors.red,
              ),
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (result['success']) ...[
                    const Text('🎉 SSL证书问题已解决！'),
                    const SizedBox(height: 8),
                    const Text('注意: 当前使用的是信任所有证书模式，这会降低安全性。建议联系代理服务提供商修复SSL证书问题。'),
                    const SizedBox(height: 8),
                  ],
                  Text('状态: ${result['message']}'),
                  if (result['response'] != null) ...[
                    const SizedBox(height: 8),
                    Text('响应: ${result['response']}'),
                  ],
                  if (result['duration'] != null) ...[
                    const SizedBox(height: 8),
                    Text('耗时: ${result['duration']}ms'),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示错误信息
      if (mounted) {
        Get.snackbar('错误', 'SSL修复模式测试失败: $e');
      }
    }
  }

  // 显示SSL重试对话框
  Future<bool?> _showSSLRetryDialog() async {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🔒 SSL证书验证失败'),
        content: const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('检测到SSL证书验证失败，这通常是由于代理服务器的证书配置问题导致的。'),
            SizedBox(height: 12),
            Text('您可以尝试以下解决方案:'),
            SizedBox(height: 8),
            Text('1. 使用SSL修复模式（信任所有证书）'),
            Text('2. 联系代理服务提供商修复证书问题'),
            Text('3. 更换其他代理服务'),
            SizedBox(height: 12),
            Text('⚠️ 注意: SSL修复模式会降低安全性，仅建议用于测试。',
                 style: TextStyle(color: Colors.orange, fontWeight: FontWeight.bold)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('使用SSL修复模式'),
          ),
        ],
      ),
    );
  }

  // 测试实际AI调用
  Future<void> _testActualAICall() async {
    final currentModel = controller.getCurrentModel();

    if (currentModel.apiKey.isEmpty) {
      Get.snackbar('错误', '请先配置API密钥');
      return;
    }

    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在测试实际AI调用...'),
            ],
          ),
        ),
      );

      // 使用AI服务进行实际调用
      final aiService = AIService(controller);
      final result = await aiService.generateText(
        systemPrompt: '你是一个测试助手',
        userPrompt: '请简单回答"测试成功"',
        maxTokens: 50,
        temperature: 0.1,
      );

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示结果
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text(
              '🤖 实际AI调用结果',
              style: TextStyle(color: Colors.green),
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('✅ 调用成功！'),
                  const SizedBox(height: 8),
                  Text('响应: $result'),
                  const SizedBox(height: 8),
                  const Text(
                    '这证明系统代理配置已生效！',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示错误信息
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text(
              '❌ 实际AI调用失败',
              style: TextStyle(color: Colors.red),
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('调用失败，错误信息：'),
                  const SizedBox(height: 8),
                  Text(
                    e.toString(),
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.red,
                      fontFamily: 'monospace',
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '请检查控制台日志，看是否使用了系统代理配置',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
        );
      }
    }
  }

  // 详细诊断
  Future<void> _comprehensiveDiagnosis() async {
    final currentModel = controller.getCurrentModel();

    if (currentModel.apiKey.isEmpty) {
      Get.snackbar('错误', '请先配置API密钥');
      return;
    }

    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在进行详细诊断...'),
              SizedBox(height: 8),
              Text(
                '这可能需要几分钟时间',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
      );

      // 执行详细诊断
      final results = <String, dynamic>{};

      // 1. DNS解析测试
      results['dns'] = await GeminiTest.testDnsResolution();

      // 2. Socket连接测试
      results['socket'] = await GeminiTest.testSocketConnection();

      // 3. 基本网络测试
      results['network'] = await GeminiTest.testNetworkConnection();

      // 4. Gemini API测试
      results['gemini'] = await GeminiTest.testBasicConnection(
        apiKey: currentModel.apiKey,
        model: currentModel.model,
      );

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示详细结果
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('🔍 详细诊断结果'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDiagnosticItem('DNS解析', results['dns']),
                  const Divider(),
                  _buildDiagnosticItem('Socket连接', results['socket']),
                  const Divider(),
                  _buildDiagnosticItem('基本网络', results['network']),
                  const Divider(),
                  _buildDiagnosticItem('Gemini API', results['gemini']),
                  const SizedBox(height: 16),
                  _buildDiagnosticSummary(results),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示错误信息
      if (mounted) {
        Get.snackbar('错误', '诊断失败: $e');
      }
    }
  }

  Widget _buildDiagnosticItem(String title, Map<String, dynamic> result) {
    final isSuccess = result['success'] ?? false;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              isSuccess ? Icons.check_circle : Icons.error,
              color: isSuccess ? Colors.green : Colors.red,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Padding(
          padding: const EdgeInsets.only(left: 28),
          child: Text(
            result['message'] ?? '无信息',
            style: TextStyle(
              fontSize: 12,
              color: isSuccess ? Colors.green[700] : Colors.red[700],
            ),
          ),
        ),
        if (result['addresses'] != null) ...[
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.only(left: 28),
            child: Text(
              'IP地址: ${result['addresses'].join(', ')}',
              style: const TextStyle(fontSize: 10, color: Colors.grey),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDiagnosticSummary(Map<String, dynamic> results) {
    final successCount = results.values.where((r) => r['success'] == true).length;
    final totalCount = results.length;

    String summary;
    Color color;

    if (successCount == totalCount) {
      summary = '✅ 所有测试通过，网络连接正常';
      color = Colors.green;
    } else if (successCount == 0) {
      summary = '❌ 所有测试失败，请检查网络设置';
      color = Colors.red;
    } else {
      summary = '⚠️ 部分测试失败，可能存在网络问题';
      color = Colors.orange;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '诊断总结',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            summary,
            style: TextStyle(color: color),
          ),
          const SizedBox(height: 8),
          Text(
            '通过率: $successCount/$totalCount',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 添加监听器，当选中的模型变化时更新文本控制器
    _modelChangeWorker ??= ever(controller.selectedModelId, (_) {
      if (mounted) {
        _updateTextControllers();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddModelDialog(context, controller),
          ),
        ],
      ),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: [
            TabBar(
              tabs: const [
                Tab(text: '模型配置'),
                Tab(text: '模型列表'),
              ],
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey,
            ),
            Expanded(
              child: TabBarView(
                children: [
                  // 第一个标签页 - 原有的模型配置
                  SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        '模型设置',
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      TextButton(
                                        onPressed: () =>
                                            controller.resetToDefaults(),
                                        child: const Text('重置默认配置'),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  Obx(() {
                                    // 创建一个模型名称到模型对象的映射，确保唯一性
                                    final Map<String, ModelConfig>
                                        uniqueModels = {};
                                    for (final model in controller.models) {
                                      uniqueModels[model.name] = model;
                                    }

                                    // 使用唯一的模型列表
                                    final uniqueModelsList =
                                        uniqueModels.values.toList();

                                    // 确保选中的模型存在于列表中
                                    String selectedValue =
                                        controller.selectedModelId.value;
                                    if (!uniqueModels
                                            .containsKey(selectedValue) &&
                                        uniqueModelsList.isNotEmpty) {
                                      selectedValue = uniqueModelsList[0].name;
                                      // 更新选中的模型
                                      Future.microtask(() => controller
                                          .updateSelectedModel(selectedValue));
                                    }

                                    return ThemedDropdownButtonFormField<
                                        String>(
                                      value: selectedValue,
                                      decoration: const InputDecoration(
                                        labelText: '选择模型',
                                        border: OutlineInputBorder(),
                                      ),
                                      items: uniqueModelsList
                                          .map((model) => DropdownMenuItem(
                                                value: model.name,
                                                child: SizedBox(
                                                  width: 300,
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      Expanded(
                                                          child:
                                                              Text(model.name)),
                                                      if (model.isCustom)
                                                        IconButton(
                                                          icon: const Icon(
                                                              Icons
                                                                  .delete_outline,
                                                              size: 20),
                                                          onPressed: () {
                                                            controller
                                                                .removeCustomModel(
                                                                    model.name);
                                                          },
                                                        ),
                                                    ],
                                                  ),
                                                ),
                                              ))
                                          .toList(),
                                      onChanged: (value) {
                                        if (value != null) {
                                          controller.updateSelectedModel(value);
                                        }
                                      },
                                    );
                                  }),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Obx(() {
                            final currentModel = controller.getCurrentModel();
                            return Card(
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '${currentModel.name} 配置',
                                      style: const TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    Obx(() => TextField(
                                      obscureText: !controller.isApiKeyVisible.value,
                                      decoration: InputDecoration(
                                        labelText: 'API Key',
                                        hintText: '请输入您的 API Key',
                                        border: const OutlineInputBorder(),
                                        suffixIcon: IconButton(
                                          icon: Icon(
                                            controller.isApiKeyVisible.value
                                                ? Icons.visibility
                                                : Icons.visibility_off,
                                          ),
                                          onPressed: controller.toggleApiKeyVisibility,
                                        ),
                                      ),
                                      onChanged: (value) =>
                                          controller.updateModelConfig(
                                        currentModel.name,
                                        apiKey: value,
                                      ),
                                      controller: _textControllers['apiKey'],
                                    )),
                                    const SizedBox(height: 16),
                                    TextField(
                                      decoration: const InputDecoration(
                                        labelText: 'API URL',
                                        hintText: '请输入 API 服务器地址',
                                        border: OutlineInputBorder(),
                                      ),
                                      onChanged: (value) =>
                                          controller.updateModelConfig(
                                        currentModel.name,
                                        apiUrl: value,
                                      ),
                                      controller: _textControllers['apiUrl'],
                                    ),
                                    const SizedBox(height: 16),
                                    TextField(
                                      decoration: const InputDecoration(
                                        labelText: 'API 路径',
                                        hintText:
                                            '请输入 API 路径（如 /v1/chat/completions）',
                                        border: OutlineInputBorder(),
                                      ),
                                      onChanged: (value) =>
                                          controller.updateModelConfig(
                                        currentModel.name,
                                        apiPath: value,
                                      ),
                                      controller: _textControllers['apiPath'],
                                    ),
                                    const SizedBox(height: 16),
                                    // 添加阿里云通义千问深度思考模式设置
                                    if (currentModel.name.contains('阿里云') ||
                                        currentModel.name.contains('通义') ||
                                        currentModel.apiUrl
                                            .contains('dashscope.aliyuncs.com'))
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const Text(
                                            '阿里云通义千问特殊设置',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          SwitchListTile(
                                            title: const Text('启用深度思考模式'),
                                            subtitle: const Text(
                                                '使模型进行更深入的思考，生成更高质量的内容'),
                                            value: currentModel.enableThinking,
                                            onChanged: (value) =>
                                                controller.updateModelConfig(
                                              currentModel.name,
                                              enableThinking: value,
                                            ),
                                          ),
                                        ],
                                      ),

                                    // 添加代理设置选项
                                    if (currentModel.apiFormat == 'Google API')
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const Text(
                                            '网络代理设置（可选）',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          SwitchListTile(
                                            title: const Text('启用代理'),
                                            subtitle: const Text(
                                                '如果已有VPN或系统代理，通常无需启用'),
                                            value: currentModel.useProxy,
                                            onChanged: (value) =>
                                                controller.updateModelConfig(
                                              currentModel.name,
                                              useProxy: value,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          TextField(
                                            decoration: const InputDecoration(
                                              labelText: '代理服务器地址',
                                              hintText: '例如: 127.0.0.1:7890',
                                              border: OutlineInputBorder(),
                                            ),
                                            onChanged: (value) =>
                                                controller.updateModelConfig(
                                              currentModel.name,
                                              proxyUrl: value,
                                            ),
                                            controller:
                                                _textControllers['proxyUrl'],
                                          ),
                                          const SizedBox(height: 8),
                                          Column(
                                            children: [
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: ElevatedButton.icon(
                                                      onPressed: () => _checkProxyStatus(),
                                                      icon: const Icon(Icons.network_check),
                                                      label: const Text('检查代理状态'),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor: Colors.blue,
                                                        foregroundColor: Colors.white,
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Expanded(
                                                    child: ElevatedButton.icon(
                                                      onPressed: () => _testGeminiConnection(),
                                                      icon: const Icon(Icons.bug_report),
                                                      label: const Text('简单测试'),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor: Colors.green,
                                                        foregroundColor: Colors.white,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 8),
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: ElevatedButton.icon(
                                                      onPressed: () => _testWithDio(),
                                                      icon: const Icon(Icons.rocket_launch),
                                                      label: const Text('Dio测试'),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor: Colors.purple,
                                                        foregroundColor: Colors.white,
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Expanded(
                                                    child: ElevatedButton.icon(
                                                      onPressed: () => _testWithSystemProxy(),
                                                      icon: const Icon(Icons.settings_ethernet),
                                                      label: const Text('系统代理'),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor: Colors.teal,
                                                        foregroundColor: Colors.white,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 8),
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: ElevatedButton.icon(
                                                      onPressed: () => _comprehensiveDiagnosis(),
                                                      icon: const Icon(Icons.medical_services),
                                                      label: const Text('详细诊断'),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor: Colors.orange,
                                                        foregroundColor: Colors.white,
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Expanded(
                                                    child: ElevatedButton.icon(
                                                      onPressed: () => _testActualAICall(),
                                                      icon: const Icon(Icons.smart_toy),
                                                      label: const Text('实际调用'),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor: Colors.red,
                                                        foregroundColor: Colors.white,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 8),
                                              // OpenAI代理SSL修复测试按钮
                                              SizedBox(
                                                width: double.infinity,
                                                child: ElevatedButton.icon(
                                                  onPressed: () => _testOpenAIProxy(),
                                                  icon: const Icon(Icons.security),
                                                  label: const Text('OpenAI代理SSL修复测试'),
                                                  style: ElevatedButton.styleFrom(
                                                    backgroundColor: Colors.deepOrange,
                                                    foregroundColor: Colors.white,
                                                    padding: const EdgeInsets.symmetric(vertical: 12),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(height: 4),
                                              const Text(
                                                '🔒 专门用于解决SSL证书验证失败问题',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.grey,
                                                  fontStyle: FontStyle.italic,
                                                ),
                                                textAlign: TextAlign.center,
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          TextField(
                                            decoration: const InputDecoration(
                                              labelText: '请求超时时间(秒)',
                                              hintText: '建议设置为60-120秒',
                                              border: OutlineInputBorder(),
                                            ),
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) =>
                                                controller.updateModelConfig(
                                              currentModel.name,
                                              timeout:
                                                  int.tryParse(value) ?? 60,
                                            ),
                                            controller:
                                                _textControllers['timeout'],
                                          ),
                                          const SizedBox(height: 16),
                                          // 添加网络测试按钮
                                          ElevatedButton.icon(
                                            icon:
                                                const Icon(Icons.network_check),
                                            label: const Text('网络连接诊断'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.blue,
                                              foregroundColor: Colors.white,
                                            ),
                                            onPressed: () {
                                              Get.to(() =>
                                                  const NetworkTestScreen());
                                            },
                                          ),
                                        ],
                                      ),
                                    const SizedBox(height: 16),
                                    ElevatedButton(
                                      onPressed: () async {
                                        // 显示加载对话框
                                        showDialog(
                                          context: context,
                                          barrierDismissible: false,
                                          builder: (context) =>
                                              const AlertDialog(
                                            content: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                CircularProgressIndicator(),
                                                SizedBox(height: 16),
                                                Text('正在验证连接...'),
                                              ],
                                            ),
                                          ),
                                        );

                                        // 验证连接
                                        final result = await controller
                                            .validateModelConnection(
                                          currentModel.name,
                                        );

                                        // 检查组件是否仍然挂载
                                        if (!mounted) return;

                                        // 关闭加载对话框
                                        Navigator.of(context).pop();

                                        // 显示结果
                                        showDialog(
                                          context: context,
                                          builder: (context) => AlertDialog(
                                            title: Text(
                                              result['success'] == true
                                                  ? '验证成功'
                                                  : '验证失败',
                                              style: TextStyle(
                                                color: result['success'] == true
                                                    ? Colors.green
                                                    : Colors.red,
                                              ),
                                            ),
                                            content: SingleChildScrollView(
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(result['message'] ?? ''),
                                                  const SizedBox(height: 16),

                                                  // 如果有生成测试结果，显示生成的内容
                                                  if (result['generationTest'] !=
                                                          null &&
                                                      result['generationTest']
                                                              ['success'] ==
                                                          true)
                                                    Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        const Text('模型生成测试结果:',
                                                            style: TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold)),
                                                        const SizedBox(
                                                            height: 8),
                                                        Container(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(8),
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Colors
                                                                .grey[200],
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          ),
                                                          child: Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Text(
                                                                  '生成内容: ${result['generationTest']['generatedText']}'),
                                                              Text(
                                                                  '响应时间: ${result['generationTest']['responseTime']}'),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),

                                                  // 如果生成测试失败，显示错误信息
                                                  if (result['generationTest'] !=
                                                          null &&
                                                      result['generationTest']
                                                              ['success'] ==
                                                          false)
                                                    Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        const Text('模型生成测试失败:',
                                                            style: TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: Colors
                                                                    .red)),
                                                        const SizedBox(
                                                            height: 8),
                                                        Container(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(8),
                                                          decoration:
                                                              BoxDecoration(
                                                            color:
                                                                Colors.red[50],
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          ),
                                                          child: Text(result[
                                                                      'generationTest']
                                                                  ['error'] ??
                                                              '未知错误'),
                                                        ),
                                                      ],
                                                    ),

                                                  // 如果有错误信息，显示错误详情
                                                  if (result['error'] != null)
                                                    Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        const SizedBox(
                                                            height: 16),
                                                        const Text('错误详情:',
                                                            style: TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: Colors
                                                                    .red)),
                                                        const SizedBox(
                                                            height: 8),
                                                        Container(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(8),
                                                          decoration:
                                                              BoxDecoration(
                                                            color:
                                                                Colors.red[50],
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          ),
                                                          child: Text(
                                                              result['error']),
                                                        ),
                                                      ],
                                                    ),
                                                ],
                                              ),
                                            ),
                                            actions: [
                                              TextButton(
                                                onPressed: () =>
                                                    Navigator.of(context).pop(),
                                                child: const Text('关闭'),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            Theme.of(context).primaryColor,
                                        foregroundColor: Colors.white,
                                      ),
                                      child: const Text('验证连接'),
                                    ),
                                    const SizedBox(height: 16),
                                    // 模型标识符组件
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Expanded(
                                              child: TextField(
                                                decoration:
                                                    const InputDecoration(
                                                  labelText: '模型标识符',
                                                  hintText: '请输入具体的模型名称',
                                                  border: OutlineInputBorder(),
                                                ),
                                                onChanged: (value) => controller
                                                    .updateModelConfig(
                                                  currentModel.name,
                                                  model: value,
                                                ),
                                                controller:
                                                    _textControllers['model'],
                                              ),
                                            ),
                                            IconButton(
                                              icon:
                                                  const Icon(Icons.add_circle),
                                              tooltip: '添加模型变体',
                                              onPressed: () {
                                                _showAddModelVariantDialog(
                                                    context,
                                                    controller,
                                                    currentModel);
                                              },
                                            ),
                                          ],
                                        ),
                                        if (currentModel
                                            .modelVariants.isNotEmpty) ...[
                                          const SizedBox(height: 8),
                                          Container(
                                            width: double.infinity,
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 12, vertical: 8),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  '可用的模型变体 (${currentModel.modelVariants.length})',
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 14,
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Wrap(
                                                  spacing: 6.0,
                                                  runSpacing: 6.0,
                                                  children: List.generate(
                                                    currentModel
                                                        .modelVariants.length,
                                                    (index) =>
                                                        _buildVariantChip(
                                                      context,
                                                      controller,
                                                      currentModel,
                                                      currentModel
                                                          .modelVariants[index],
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                    const SizedBox(height: 16),
                                    TextField(
                                      decoration: const InputDecoration(
                                        labelText: 'App ID',
                                        hintText: '请输入应用ID（百度千帆等需要）',
                                        border: OutlineInputBorder(),
                                      ),
                                      onChanged: (value) =>
                                          controller.updateModelConfig(
                                        currentModel.name,
                                        appId: value,
                                      ),
                                      controller: _textControllers['appId'],
                                    ),
                                    const SizedBox(height: 16),
                                    TextField(
                                      decoration: const InputDecoration(
                                        labelText: 'Max tokens(不要低于4000)',
                                        hintText:
                                            '建议设置在4000-8000之间，过短可能导致情节单薄，过长则生成较慢',
                                        border: OutlineInputBorder(),
                                      ),
                                      keyboardType: TextInputType.number,
                                      onChanged: (value) =>
                                          controller.updateModelConfig(
                                        currentModel.name,
                                        maxTokens: int.tryParse(value) ?? 5000,
                                      ),
                                      controller: _textControllers['maxTokens'],
                                    ),
                                    const SizedBox(height: 16),
                                    ThemedDropdownButtonFormField<String>(
                                      value: currentModel.apiFormat,
                                      decoration: const InputDecoration(
                                        labelText: 'API 格式',
                                        border: OutlineInputBorder(),
                                      ),
                                      items: const [
                                        DropdownMenuItem(
                                          value: 'OpenAI API兼容',
                                          child: Text('OpenAI API兼容'),
                                        ),
                                        DropdownMenuItem(
                                          value: 'Google API',
                                          child: Text('Google API'),
                                        ),
                                      ],
                                      onChanged: (value) {
                                        if (value != null) {
                                          controller.updateModelConfig(
                                            currentModel.name,
                                            apiFormat: value,
                                          );
                                        }
                                      },
                                    ),
                                    const SizedBox(height: 24),
                                    const Text(
                                      '高级设置',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text('温度 (Temperature)'),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Slider(
                                                value: controller
                                                    .temperature.value,
                                                min: 0.0,
                                                max: 2.0,
                                                divisions: 20,
                                                label: controller
                                                    .temperature.value
                                                    .toStringAsFixed(1),
                                                onChanged: (value) => controller
                                                    .updateTemperature(value),
                                              ),
                                            ),
                                            SizedBox(
                                              width: 60,
                                              child: Text(
                                                controller.temperature.value
                                                    .toStringAsFixed(1),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 16),
                                        const Text('Top P'),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Slider(
                                                value: controller.topP.value,
                                                min: 0.0,
                                                max: 1.0,
                                                divisions: 10,
                                                label: controller.topP.value
                                                    .toStringAsFixed(1),
                                                onChanged: (value) => controller
                                                    .updateTopP(value),
                                              ),
                                            ),
                                            SizedBox(
                                              width: 60,
                                              child: Text(
                                                controller.topP.value
                                                    .toStringAsFixed(1),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 16),
                                        const Text('重复惩罚 (Repetition Penalty)'),
                                        const Text(
                                          '控制文本重复的程度，值越大越不容易重复\n建议范围：1.0-1.5，默认1.3',
                                          style: TextStyle(
                                              fontSize: 12, color: Colors.grey),
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Slider(
                                                value: controller
                                                    .repetitionPenalty.value,
                                                min: 1.0,
                                                max: 2.0,
                                                divisions: 20,
                                                label: controller
                                                    .repetitionPenalty.value
                                                    .toStringAsFixed(2),
                                                onChanged: (value) => controller
                                                    .updateRepetitionPenalty(
                                                        value),
                                              ),
                                            ),
                                            SizedBox(
                                              width: 60,
                                              child: Text(
                                                controller
                                                    .repetitionPenalty.value
                                                    .toStringAsFixed(2),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 16),
                                        const Text('最大生成长度'),
                                        const Text(
                                          '控制每章生成的最大长度，建议4000-8000之间\n数值越大生成内容越长，但速度也越慢',
                                          style: TextStyle(
                                              fontSize: 12, color: Colors.grey),
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Slider(
                                                value: currentModel.maxTokens
                                                    .toDouble(),
                                                min: 2000,
                                                max: 16384,
                                                divisions: 144,
                                                label:
                                                    '${currentModel.maxTokens} tokens',
                                                onChanged: (value) {
                                                  final tokens = value.toInt();
                                                  controller
                                                      .updateMaxTokens(tokens);
                                                  controller.updateModelConfig(
                                                    currentModel.name,
                                                    maxTokens: tokens,
                                                  );
                                                },
                                              ),
                                            ),
                                            SizedBox(
                                              width: 100,
                                              child: Text(
                                                '${currentModel.maxTokens}\ntokens',
                                                textAlign: TextAlign.center,
                                                style: const TextStyle(
                                                    height: 1.2),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }),

                          // 添加双模型模式区域
                          const SizedBox(height: 16),
                          const Card(
                            child: Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '双模型模式',
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    '开启后可分别选择用于大纲和章节生成的模型，实现高性能大纲生成和稳定章节生成',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey,
                                    ),
                                  ),
                                  SizedBox(height: 16),
                                  // 移除双模型模式开关
                                  // Obx(() => SwitchListTile(
                                  //   title: const Text('启用双模型模式'),
                                  //   subtitle: const Text('分别为大纲和章节选择不同模型'),
                                  //   value: controller.isDualModelMode.value,
                                  //   onChanged: (value) {
                                  //     controller.isDualModelMode.value = value;
                                  //     controller.saveDualModelConfig();
                                  //   },
                                  // )),

                                  // 移除双模型模式相关UI
                                  // Obx(() => controller.isDualModelMode.value
                                  // ? Column(
                                  //     crossAxisAlignment: CrossAxisAlignment.start,
                                  //     children: [
                                  //       const SizedBox(height: 16),
                                  //       const Text(
                                  //         '大纲生成模型',
                                  //         style: TextStyle(
                                  //           fontSize: 16,
                                  //           fontWeight: FontWeight.bold,
                                  //         ),
                                  //       ),
                                  //       const SizedBox(height: 8),
                                  //       DropdownButtonFormField<String>(
                                  //         value: controller.outlineModelId.value.isEmpty
                                  //             ? controller.selectedModelId.value
                                  //             : controller.outlineModelId.value,
                                  //         decoration: const InputDecoration(
                                  //           labelText: '选择大纲生成模型',
                                  //           helperText: '推荐选择高性能模型，如GPT-4或Deepseek-Reasoner',
                                  //           border: OutlineInputBorder(),
                                  //         ),
                                  //         items: controller.models.map((model) => DropdownMenuItem(
                                  //           value: model.name,
                                  //           child: Text(model.name),
                                  //         )).toList(),
                                  //         onChanged: (value) {
                                  //           if (value != null) {
                                  //             controller.outlineModelId.value = value;
                                  //             controller.outlineModelVariant.value = ''; // 清空之前的变体选择
                                  //             controller.saveDualModelConfig();
                                  //           }
                                  //         },
                                  //       ),
                                  //
                                  //       // 添加大纲模型变体选择
                                  //       Obx(() {
                                  //         // 获取当前选择的大纲模型
                                  //         final outlineModel = controller.models.firstWhere(
                                  //           (model) => model.name == (controller.outlineModelId.value.isEmpty
                                  //               ? controller.selectedModelId.value
                                  //               : controller.outlineModelId.value),
                                  //           orElse: () => controller.models.first,
                                  //         );
                                  //
                                  //         // 如果该模型有变体，显示变体选择
                                  //         if (outlineModel.modelVariants.isNotEmpty) {
                                  //           return Padding(
                                  //             padding: const EdgeInsets.only(top: 16.0),
                                  //             child: DropdownButtonFormField<String>(
                                  //               value: controller.outlineModelVariant.value.isEmpty
                                  //                   ? outlineModel.model  // 默认使用模型当前值
                                  //                   : controller.outlineModelVariant.value,
                                  //               decoration: const InputDecoration(
                                  //                 labelText: '选择大纲模型变体',
                                  //                 helperText: '不同的模型变体可能有不同的性能和特点',
                                  //                 border: OutlineInputBorder(),
                                  //               ),
                                  //               items: [
                                  //                 DropdownMenuItem(
                                  //                   value: outlineModel.model,
                                  //                   child: Text('${outlineModel.model} (默认)'),
                                  //                 ),
                                  //                 ...outlineModel.modelVariants.map((variant) => DropdownMenuItem(
                                  //                   value: variant,
                                  //                   child: Text(variant),
                                  //                 )).toList(),
                                  //               ],
                                  //               onChanged: (value) {
                                  //                 if (value != null) {
                                  //                   controller.outlineModelVariant.value = value;
                                  //                   controller.saveDualModelConfig();
                                  //                 }
                                  //               },
                                  //             ),
                                  //           );
                                  //         }
                                  //         return const SizedBox.shrink();
                                  //       }),
                                  //
                                  //       const SizedBox(height: 24),
                                  //       const Text(
                                  //         '章节生成模型',
                                  //         style: TextStyle(
                                  //           fontSize: 16,
                                  //           fontWeight: FontWeight.bold,
                                  //         ),
                                  //       ),
                                  //       const SizedBox(height: 8),
                                  //       DropdownButtonFormField<String>(
                                  //         value: controller.chapterModelId.value.isEmpty
                                  //             ? controller.selectedModelId.value
                                  //             : controller.chapterModelId.value,
                                  //         decoration: const InputDecoration(
                                  //           labelText: '选择章节生成模型',
                                  //           helperText: '推荐选择稳定性高的模型，如Qwen或GPT-3.5',
                                  //           border: OutlineInputBorder(),
                                  //         ),
                                  //         items: controller.models.map((model) => DropdownMenuItem(
                                  //           value: model.name,
                                  //           child: Text(model.name),
                                  //         )).toList(),
                                  //         onChanged: (value) {
                                  //           if (value != null) {
                                  //             controller.chapterModelId.value = value;
                                  //             controller.chapterModelVariant.value = ''; // 清空之前的变体选择
                                  //             controller.saveDualModelConfig();
                                  //           }
                                  //         },
                                  //       ),
                                  //
                                  //       // 添加章节模型变体选择
                                  //       Obx(() {
                                  //         // 获取当前选择的章节模型
                                  //         final chapterModel = controller.models.firstWhere(
                                  //           (model) => model.name == (controller.chapterModelId.value.isEmpty
                                  //               ? controller.selectedModelId.value
                                  //               : controller.chapterModelId.value),
                                  //           orElse: () => controller.models.first,
                                  //         );
                                  //
                                  //         // 如果该模型有变体，显示变体选择
                                  //         if (chapterModel.modelVariants.isNotEmpty) {
                                  //           return Padding(
                                  //             padding: const EdgeInsets.only(top: 16.0),
                                  //             child: DropdownButtonFormField<String>(
                                  //               value: controller.chapterModelVariant.value.isEmpty
                                  //                   ? chapterModel.model  // 默认使用模型当前值
                                  //                   : controller.chapterModelVariant.value,
                                  //               decoration: const InputDecoration(
                                  //                 labelText: '选择章节模型变体',
                                  //                 helperText: '不同的模型变体可能有不同的稳定性和生成风格',
                                  //                 border: OutlineInputBorder(),
                                  //               ),
                                  //               items: [
                                  //                 DropdownMenuItem(
                                  //                   value: chapterModel.model,
                                  //                   child: Text('${chapterModel.model} (默认)'),
                                  //                 ),
                                  //                 ...chapterModel.modelVariants.map((variant) => DropdownMenuItem(
                                  //                   value: variant,
                                  //                   child: Text(variant),
                                  //                 )).toList(),
                                  //               ],
                                  //               onChanged: (value) {
                                  //                 if (value != null) {
                                  //                   controller.chapterModelVariant.value = value;
                                  //                   controller.saveDualModelConfig();
                                  //                 }
                                  //               },
                                  //             ),
                                  //           );
                                  //         }
                                  //         return const SizedBox.shrink();
                                  //       }),
                                  //     ],
                                  //   )
                                  //   : const SizedBox.shrink()
                                  // ),
                                ],
                              ),
                            ),
                          ),
                          // 添加嵌入模型配置卡片
                          const SizedBox(height: 16),
                          Obx(() => Card(
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        '嵌入模型配置',
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      const Text(
                                        '启用嵌入模型可以提高章节生成的连贯性，避免重复内容',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      SwitchListTile(
                                        title: const Text('启用嵌入模型'),
                                        subtitle: const Text('使用嵌入模型改善章节生成质量'),
                                        value: controller
                                            .embeddingModel.value.enabled,
                                        onChanged: (value) {
                                          controller.updateEmbeddingModel(
                                              enabled: value);
                                        },
                                      ),
                                      const SizedBox(height: 16),

                                      // 嵌入模型选择
                                      const Text(
                                        '选择嵌入模型类型',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: RadioListTile<bool>(
                                              title: const Text('岱宗官方（限时免费）'),
                                              value: true,
                                              groupValue: controller
                                                      .embeddingModel
                                                      .value
                                                      .apiKey ==
                                                  'daizong_official_embedding_key',
                                              onChanged: (value) {
                                                if (value == true) {
                                                  // 切换到岱宗官方嵌入模型
                                                  controller
                                                      .updateEmbeddingModel(
                                                    name: '岱宗官方嵌入模型（限时免费）',
                                                    apiKey:
                                                        'daizong_official_embedding_key',
                                                    baseUrl:
                                                        'https://dashscope.aliyuncs.com',
                                                    apiPath:
                                                        '/compatible-mode/v1/embeddings',
                                                    modelName:
                                                        'text-embedding-v3',
                                                    apiFormat: 'OpenAI API兼容',
                                                    enabled: true,
                                                  );
                                                }
                                              },
                                            ),
                                          ),
                                          Expanded(
                                            child: RadioListTile<bool>(
                                              title: const Text('自定义模型'),
                                              value: false,
                                              groupValue: controller
                                                      .embeddingModel
                                                      .value
                                                      .apiKey ==
                                                  'daizong_official_embedding_key',
                                              onChanged: (value) {
                                                if (value == false) {
                                                  // 切换到自定义嵌入模型
                                                  final customModel =
                                                      EmbeddingModelConfig
                                                          .getCustom();
                                                  controller
                                                      .updateEmbeddingModel(
                                                    name: customModel.name,
                                                    apiKey: customModel.apiKey,
                                                    baseUrl:
                                                        customModel.baseUrl,
                                                    apiPath:
                                                        customModel.apiPath,
                                                    modelName:
                                                        customModel.modelName,
                                                    apiFormat:
                                                        customModel.apiFormat,
                                                  );
                                                }
                                              },
                                            ),
                                          ),
                                        ],
                                      ),

                                      const SizedBox(height: 16),
                                      const Divider(),
                                      const SizedBox(height: 16),

                                      // 只有在选择自定义模型时才显示这些字段
                                      Visibility(
                                        visible: controller
                                                .embeddingModel.value.apiKey !=
                                            'daizong_official_embedding_key',
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const Text(
                                              '自定义模型设置',
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(height: 16),
                                            TextField(
                                              decoration: const InputDecoration(
                                                labelText: '嵌入模型名称',
                                                hintText: '请输入嵌入模型名称',
                                                border: OutlineInputBorder(),
                                              ),
                                              onChanged: (value) => controller
                                                  .updateEmbeddingModel(
                                                      name: value),
                                              controller: TextEditingController(
                                                text: controller
                                                    .embeddingModel.value.name,
                                              ),
                                            ),
                                            const SizedBox(height: 16),
                                            Obx(() => TextField(
                                              obscureText: !controller.isEmbeddingApiKeyVisible.value,
                                              decoration: InputDecoration(
                                                labelText: 'API Key',
                                                hintText: '请输入嵌入模型的 API Key',
                                                border: const OutlineInputBorder(),
                                                suffixIcon: IconButton(
                                                  icon: Icon(
                                                    controller.isEmbeddingApiKeyVisible.value
                                                        ? Icons.visibility
                                                        : Icons.visibility_off,
                                                  ),
                                                  onPressed: controller.toggleEmbeddingApiKeyVisibility,
                                                ),
                                              ),
                                              onChanged: (value) => controller
                                                  .updateEmbeddingModel(
                                                      apiKey: value),
                                              controller: TextEditingController(
                                                text: controller.embeddingModel
                                                            .value.apiKey ==
                                                        'daizong_official_embedding_key'
                                                    ? ''
                                                    : controller.embeddingModel
                                                        .value.apiKey,
                                              ),
                                            )),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      TextField(
                                        decoration: const InputDecoration(
                                          labelText: '基础 URL',
                                          hintText: '请输入嵌入模型的基础 URL',
                                          border: OutlineInputBorder(),
                                        ),
                                        onChanged: (value) =>
                                            controller.updateEmbeddingModel(
                                                baseUrl: value),
                                        controller: TextEditingController(
                                          text: controller
                                              .embeddingModel.value.baseUrl,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      TextField(
                                        decoration: const InputDecoration(
                                          labelText: 'API 路径',
                                          hintText: '请输入嵌入模型的 API 路径',
                                          border: OutlineInputBorder(),
                                        ),
                                        onChanged: (value) =>
                                            controller.updateEmbeddingModel(
                                                apiPath: value),
                                        controller: TextEditingController(
                                          text: controller
                                              .embeddingModel.value.apiPath,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      TextField(
                                        decoration: const InputDecoration(
                                          labelText: '模型名称',
                                          hintText: '请输入嵌入模型的模型名称',
                                          border: OutlineInputBorder(),
                                        ),
                                        onChanged: (value) =>
                                            controller.updateEmbeddingModel(
                                                modelName: value),
                                        controller: TextEditingController(
                                          text: controller
                                              .embeddingModel.value.modelName,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      ThemedDropdownButtonFormField<String>(
                                        value: controller
                                            .embeddingModel.value.apiFormat,
                                        decoration: const InputDecoration(
                                          labelText: 'API 格式',
                                          border: OutlineInputBorder(),
                                        ),
                                        items: const [
                                          DropdownMenuItem(
                                            value: 'OpenAI API兼容',
                                            child: Text('OpenAI API兼容'),
                                          ),
                                          DropdownMenuItem(
                                            value: 'Google API',
                                            child: Text('Google API'),
                                          ),
                                          DropdownMenuItem(
                                            value: '阿里百炼',
                                            child: Text('阿里百炼'),
                                          ),
                                        ],
                                        onChanged: (value) {
                                          if (value != null) {
                                            controller.updateEmbeddingModel(
                                                apiFormat: value);
                                          }
                                        },
                                      ),
                                      const SizedBox(height: 16),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Slider(
                                              value: controller
                                                  .embeddingModel.value.topK
                                                  .toDouble(),
                                              min: 1,
                                              max: 20,
                                              divisions: 19,
                                              label: controller
                                                  .embeddingModel.value.topK
                                                  .toString(),
                                              onChanged: (value) {
                                                controller.updateEmbeddingModel(
                                                    topK: value.toInt());
                                              },
                                            ),
                                          ),
                                          SizedBox(
                                            width: 60,
                                            child: Text(
                                              controller
                                                  .embeddingModel.value.topK
                                                  .toString(),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const Text('检索返回文档数量 (Top-K)',
                                          style: TextStyle(fontSize: 14)),

                                      // 添加代理设置
                                      const SizedBox(height: 16),
                                      const Divider(),
                                      const SizedBox(height: 8),
                                      const Text('连接设置',
                                          style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold)),
                                      const SizedBox(height: 16),

                                      // 代理开关
                                      SwitchListTile(
                                        title: const Text('使用代理'),
                                        subtitle:
                                            const Text('如果无法直接访问 API，可以尝试使用代理'),
                                        value: controller
                                            .embeddingModel.value.useProxy,
                                        onChanged: (value) {
                                          controller.updateEmbeddingModel(
                                              useProxy: value);
                                        },
                                      ),

                                      // 代理服务器设置
                                      Obx(() => controller
                                              .embeddingModel.value.useProxy
                                          ? Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 8.0),
                                              child: TextField(
                                                decoration:
                                                    const InputDecoration(
                                                  labelText: '代理服务器',
                                                  hintText:
                                                      '输入代理服务器地址，如 127.0.0.1:7890',
                                                  border: OutlineInputBorder(),
                                                ),
                                                onChanged: (value) => controller
                                                    .updateEmbeddingModel(
                                                        proxyUrl: value),
                                                controller:
                                                    TextEditingController(
                                                  text: controller
                                                      .embeddingModel
                                                      .value
                                                      .proxyUrl,
                                                ),
                                              ),
                                            )
                                          : const SizedBox.shrink()),

                                      // 超时设置
                                      const SizedBox(height: 16),
                                      Row(
                                        children: [
                                          const Text('请求超时时间: '),
                                          Expanded(
                                            child: Slider(
                                              value: controller
                                                  .embeddingModel.value.timeout
                                                  .toDouble(),
                                              min: 5,
                                              max: 120,
                                              divisions: 23,
                                              label:
                                                  '${controller.embeddingModel.value.timeout}秒',
                                              onChanged: (value) {
                                                controller.updateEmbeddingModel(
                                                    timeout: value.toInt());
                                              },
                                            ),
                                          ),
                                          SizedBox(
                                            width: 60,
                                            child: Text(
                                              '${controller.embeddingModel.value.timeout}秒',
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ],
                                      ),

                                      // 添加验证按钮
                                      const SizedBox(height: 24),
                                      ElevatedButton.icon(
                                        icon: const Icon(Icons.check_circle),
                                        label: const Text('验证连接'),
                                        style: ElevatedButton.styleFrom(
                                          minimumSize:
                                              const Size(double.infinity, 48),
                                        ),
                                        onPressed: () async {
                                          // 显示加载对话框
                                          Get.dialog(
                                            const Center(
                                              child:
                                                  CircularProgressIndicator(),
                                            ),
                                            barrierDismissible: false,
                                          );

                                          try {
                                            // 验证连接
                                            final result = await controller
                                                .validateEmbeddingConnection();

                                            // 关闭加载对话框
                                            Get.back();

                                            // 显示结果
                                            Get.dialog(
                                              AlertDialog(
                                                title: Text(
                                                  result['success']
                                                      ? '验证成功'
                                                      : '验证失败',
                                                  style: TextStyle(
                                                    color: result['success']
                                                        ? Colors.green
                                                        : Colors.red,
                                                  ),
                                                ),
                                                content:
                                                    Text(result['message']),
                                                actions: [
                                                  TextButton(
                                                    onPressed: () => Get.back(),
                                                    child: const Text('关闭'),
                                                  ),
                                                  if (result['success'])
                                                    TextButton(
                                                      onPressed: () {
                                                        Get.back();
                                                        // 如果验证成功，自动启用嵌入模型
                                                        if (!controller
                                                            .embeddingModel
                                                            .value
                                                            .enabled) {
                                                          controller
                                                              .updateEmbeddingModel(
                                                                  enabled:
                                                                      true);
                                                          Get.snackbar(
                                                              '提示', '嵌入模型已启用');
                                                        }
                                                      },
                                                      child:
                                                          const Text('启用嵌入模型'),
                                                    ),
                                                  // 如果验证失败且错误中包含关于API密钥的信息，显示获取API密钥的按钮
                                                  if (!result['success'] &&
                                                      (result['message']
                                                              .toString()
                                                              .contains(
                                                                  'API密钥') ||
                                                          result['message']
                                                              .toString()
                                                              .contains(
                                                                  '401') ||
                                                          result['message']
                                                              .toString()
                                                              .contains(
                                                                  'UNAUTHENTICATED')))
                                                    TextButton(
                                                      onPressed: () async {
                                                        // 打开Google AI Studio的API密钥页面
                                                        final Uri url = Uri.parse(
                                                            'https://makersuite.google.com/app/apikey');
                                                        try {
                                                          await url_launcher.launchUrl(
                                                              url,
                                                              mode: url_launcher
                                                                  .LaunchMode
                                                                  .externalApplication);
                                                        } catch (e) {
                                                          Get.snackbar('打开页面失败',
                                                              '无法打开浏览器: $e');
                                                        }
                                                      },
                                                      child:
                                                          const Text('获取API密钥'),
                                                    ),
                                                ],
                                              ),
                                            );
                                          } catch (e) {
                                            // 关闭加载对话框
                                            Get.back();

                                            // 显示错误
                                            Get.snackbar('验证失败', e.toString(),
                                                backgroundColor:
                                                    Colors.red.withAlpha(25),
                                                duration:
                                                    const Duration(seconds: 3));
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              )),

                          // Apply模型配置卡片
                          const SizedBox(height: 16),
                          _buildApplyModelConfigCard(context, controller),

                          // 在所有配置卡片下方添加清除缓存按钮
                          const SizedBox(height: 16),
                          Card(
                            child: ListTile(
                              leading: const Icon(Icons.delete_sweep_outlined,
                                  color: Colors.red),
                              title: const Text('清除应用缓存'),
                              subtitle: const Text('清除所有本地缓存和会话数据'),
                              onTap: () {
                                // 显示确认对话框
                                Get.dialog(
                                  AlertDialog(
                                    title: const Text('确认清除缓存？'),
                                    content: const Text(
                                        '这将清除所有本地保存的小说生成历史、会话数据和其他缓存。此操作不可恢复。'),
                                    actions: [
                                      TextButton(
                                        child: const Text('取消'),
                                        onPressed: () => Get.back(),
                                      ),
                                      TextButton(
                                        child: const Text('确认清除',
                                            style:
                                                TextStyle(color: Colors.red)),
                                        onPressed: () {
                                          Get.back(); // 关闭对话框
                                          // 调用正确的清除缓存方法，无需 await
                                          novelController.clearCache();
                                        },
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // 第二个标签页 - 模型列表
                  SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Obx(() => Column(
                          children: List.generate(
                              controller.models.length,
                              (index) => _buildModelDetailCard(
                                  context, controller, index)),
                        )),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 创建模型变体芯片
  Widget _buildVariantChip(BuildContext context, ApiConfigController controller,
      ModelConfig model, String variant) {
    final bool isCurrentModel = variant == model.model;

    return Chip(
      backgroundColor: isCurrentModel
          ? Theme.of(context).primaryColor.withOpacity(0.2)
          : Colors.grey.shade200,
      label: Text(
        variant,
        style: TextStyle(
          color:
              isCurrentModel ? Theme.of(context).primaryColor : Colors.black87,
          fontWeight: isCurrentModel ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      deleteIcon: Icon(
        isCurrentModel ? Icons.check : Icons.close,
        size: 18,
        color: isCurrentModel ? Theme.of(context).primaryColor : Colors.black54,
      ),
      onDeleted: () {
        if (isCurrentModel) {
          // 如果是当前使用的模型变体，只显示勾号不做操作
          return;
        }
        // 确认是否删除变体
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('确认删除'),
            content: Text('确定要删除模型变体 "$variant" 吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  controller.removeModelVariant(model.name, variant);
                  Navigator.of(context).pop();
                },
                child: const Text('删除'),
              ),
            ],
          ),
        );
      },
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
    );
  }

  void _showAddModelDialog(
      BuildContext context, ApiConfigController controller) {
    final nameController = TextEditingController();
    final apiKeyController = TextEditingController();
    final apiUrlController = TextEditingController();
    final apiPathController = TextEditingController();
    final modelController = TextEditingController();
    final appIdController = TextEditingController();
    final variantController = TextEditingController();
    String selectedApiFormat = 'OpenAI API兼容';

    List<String> modelVariants = [];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(builder: (context, setState) {
        return AlertDialog(
          title: const Text('添加自定义模型'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: '模型名称',
                    hintText: '请输入模型显示名称',
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: apiKeyController,
                  decoration: const InputDecoration(
                    labelText: 'API Key',
                    hintText: '请输入 API Key',
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: apiUrlController,
                  decoration: const InputDecoration(
                    labelText: 'API URL',
                    hintText: '请输入 API 服务器地址',
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: apiPathController,
                  decoration: const InputDecoration(
                    labelText: 'API Path',
                    hintText: '例如: /v1/chat/completions',
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: modelController,
                  decoration: const InputDecoration(
                    labelText: '模型标识符',
                    hintText: '请输入模型标识符',
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: variantController,
                        decoration: const InputDecoration(
                          labelText: '模型变体',
                          hintText: '添加可选的模型变体标识符',
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.add_circle),
                      onPressed: () {
                        if (variantController.text.isNotEmpty &&
                            !modelVariants.contains(variantController.text)) {
                          setState(() {
                            modelVariants.add(variantController.text);
                            variantController.clear();
                          });
                        }
                      },
                    ),
                  ],
                ),
                if (modelVariants.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  const Text('已添加的模型变体:'),
                  SizedBox(
                    height: modelVariants.length > 3 ? 120 : null,
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: modelVariants.length,
                      itemBuilder: (context, index) {
                        return ListTile(
                          dense: true,
                          contentPadding: EdgeInsets.zero,
                          title: Text(modelVariants[index]),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete, size: 20),
                            onPressed: () {
                              setState(() {
                                modelVariants.removeAt(index);
                              });
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                TextField(
                  controller: appIdController,
                  decoration: const InputDecoration(
                    labelText: 'App ID',
                    hintText: '部分模型需要App ID',
                  ),
                ),
                const SizedBox(height: 8),
                ThemedDropdownButtonFormField<String>(
                  value: selectedApiFormat,
                  decoration: const InputDecoration(
                    labelText: 'API 格式',
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'OpenAI API兼容',
                      child: Text('OpenAI API兼容'),
                    ),
                    DropdownMenuItem(
                      value: 'Google API',
                      child: Text('Google API'),
                    ),
                    DropdownMenuItem(
                      value: '阿里百炼',
                      child: Text('阿里百炼'),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedApiFormat = value;
                      });
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                if (nameController.text.isEmpty) {
                  Get.snackbar(
                    '提示',
                    '请输入模型名称',
                    backgroundColor: Colors.red.withOpacity(0.1),
                    duration: const Duration(seconds: 2),
                  );
                  return;
                }

                if (controller.models
                    .any((m) => m.name == nameController.text)) {
                  Get.snackbar(
                    '提示',
                    '已存在同名模型，请使用其他名称',
                    backgroundColor: Colors.red.withOpacity(0.1),
                    duration: const Duration(seconds: 2),
                  );
                  return;
                }

                controller.addCustomModel(ModelConfig(
                  name: nameController.text,
                  apiKey: apiKeyController.text,
                  apiUrl: apiUrlController.text,
                  apiPath: apiPathController.text,
                  model: modelController.text,
                  modelVariants: modelVariants,
                  appId: appIdController.text,
                  apiFormat: selectedApiFormat,
                  isCustom: true,
                ));

                Navigator.of(context).pop();
                Get.snackbar(
                  '成功',
                  '模型添加成功',
                  backgroundColor: Colors.green.withOpacity(0.1),
                  duration: const Duration(seconds: 2),
                );
              },
              child: const Text('确定'),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildModelDetailCard(
      BuildContext context, ApiConfigController controller, int index) {
    final model = controller.models[index];
    final isCurrentSelected = model.name == controller.selectedModelId.value;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      color: isCurrentSelected ? Colors.blue.shade50 : null,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  model.name,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: isCurrentSelected ? FontWeight.bold : null,
                        color: isCurrentSelected
                            ? Theme.of(context).primaryColor
                            : null,
                      ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 选择此模型按钮
                    if (!isCurrentSelected)
                      IconButton(
                        icon: const Icon(Icons.check_circle_outline),
                        tooltip: '使用此模型',
                        onPressed: () {
                          controller.updateSelectedModel(model.name);
                          Get.snackbar(
                            '已切换模型',
                            '当前使用模型: ${model.name}',
                            backgroundColor: Colors.green.withOpacity(0.1),
                            duration: const Duration(seconds: 2),
                          );
                        },
                      ),
                    if (model.isCustom)
                      IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        tooltip: '删除此模型',
                        onPressed: () {
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('确认删除'),
                              content:
                                  Text('确定要删除模型 "${model.name}" 吗？此操作不可撤销。'),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  child: const Text('取消'),
                                ),
                                TextButton(
                                  onPressed: () {
                                    controller.removeCustomModel(model.name);
                                    Navigator.of(context).pop();
                                  },
                                  child: const Text('删除'),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            // 当前模型标识符
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('当前模型标识符:',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 4),
                      Text(model.model),
                    ],
                  ),
                ),
                // 编辑按钮
                IconButton(
                  icon: const Icon(Icons.edit),
                  tooltip: '编辑模型标识符',
                  onPressed: () {
                    _showEditModelIdentifierDialog(context, controller, model);
                  },
                ),
              ],
            ),

            // 模型变体列表
            if (model.modelVariants.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text('可用模型变体:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 4),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: model.modelVariants.length,
                  itemBuilder: (context, variantIndex) {
                    final variant = model.modelVariants[variantIndex];
                    final isCurrentModel = variant == model.model;

                    return ListTile(
                      dense: true,
                      title: Text(
                        variant,
                        style: TextStyle(
                          fontWeight: isCurrentModel
                              ? FontWeight.bold
                              : FontWeight.normal,
                          color: isCurrentModel
                              ? Theme.of(context).primaryColor
                              : null,
                        ),
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 使用此变体按钮
                          if (!isCurrentModel)
                            IconButton(
                              icon: const Icon(Icons.check_circle_outline,
                                  size: 20),
                              onPressed: () {
                                controller.updateModelIdentifier(
                                    model.name, variant);
                              },
                              tooltip: '使用此模型标识符',
                            ),
                          // 删除变体按钮
                          IconButton(
                            icon: const Icon(Icons.delete,
                                size: 20, color: Colors.red),
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title: const Text('确认删除'),
                                  content: Text('确定要删除模型变体 "$variant" 吗？'),
                                  actions: [
                                    TextButton(
                                      onPressed: () =>
                                          Navigator.of(context).pop(),
                                      child: const Text('取消'),
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        controller.removeModelVariant(
                                            model.name, variant);
                                        Navigator.of(context).pop();
                                      },
                                      child: const Text('删除'),
                                    ),
                                  ],
                                ),
                              );
                            },
                            tooltip: '删除此变体',
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],

            // 添加模型变体按钮
            const SizedBox(height: 8),
            OutlinedButton.icon(
              icon: const Icon(Icons.add),
              label: const Text('添加模型变体'),
              onPressed: () {
                _showAddModelVariantDialog(context, controller, model);
              },
            ),

            const SizedBox(height: 12),
            const Divider(),

            // 模型API配置信息
            Text('API URL: ${model.apiUrl}'),
            const SizedBox(height: 4),
            Text('API 路径: ${model.apiPath}'),
            const SizedBox(height: 4),
            Text('API 格式: ${model.apiFormat}'),
            if (model.appId.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text('App ID: ${model.appId}'),
            ],
            // 显示阿里云通义千问特有的深度思考模式状态
            if (model.name.contains('阿里云') ||
                model.name.contains('通义') ||
                model.apiUrl.contains('dashscope.aliyuncs.com')) ...[
              const SizedBox(height: 4),
              Text('深度思考模式: ${model.enableThinking ? '已启用' : '未启用'}'),
            ],
          ],
        ),
      ),
    );
  }

  // 显示添加模型变体对话框
  void _showAddModelVariantDialog(
      BuildContext context, ApiConfigController controller, ModelConfig model) {
    final textController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加模型变体'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: textController,
              decoration: const InputDecoration(
                labelText: '模型变体标识符',
                hintText: '请输入新的模型标识符',
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              if (textController.text.isEmpty) {
                Get.snackbar(
                  '提示',
                  '请输入模型变体标识符',
                  backgroundColor: Colors.red.withOpacity(0.1),
                  duration: const Duration(seconds: 2),
                );
                return;
              }

              if (model.modelVariants.contains(textController.text) ||
                  model.model == textController.text) {
                Get.snackbar(
                  '提示',
                  '该模型变体已存在',
                  backgroundColor: Colors.red.withOpacity(0.1),
                  duration: const Duration(seconds: 2),
                );
                return;
              }

              controller.addModelVariant(model.name, textController.text);
              Navigator.of(context).pop();
              Get.snackbar(
                '成功',
                '模型变体添加成功',
                backgroundColor: Colors.green.withOpacity(0.1),
                duration: const Duration(seconds: 2),
              );
            },
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }

  // 显示编辑模型标识符对话框
  void _showEditModelIdentifierDialog(
      BuildContext context, ApiConfigController controller, ModelConfig model) {
    final textController = TextEditingController(text: model.model);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑模型标识符'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: textController,
              decoration: const InputDecoration(
                labelText: '模型标识符',
                hintText: '请输入模型标识符',
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              if (textController.text.isEmpty) {
                Get.snackbar(
                  '提示',
                  '请输入模型标识符',
                  backgroundColor: Colors.red.withOpacity(0.1),
                  duration: const Duration(seconds: 2),
                );
                return;
              }

              controller.updateModelIdentifier(model.name, textController.text);
              Navigator.of(context).pop();
              Get.snackbar(
                '成功',
                '模型标识符已更新',
                backgroundColor: Colors.green.withOpacity(0.1),
                duration: const Duration(seconds: 2),
              );
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  // 当前模型变体使用情况
  Widget _buildModelVariantUsageInfo(
      BuildContext context, ApiConfigController controller) {
    final List<Widget> items = [];

    // 获取所有有变体的模型
    final modelsWithVariants = controller.models
        .where((model) => model.modelVariants.isNotEmpty)
        .toList();

    if (modelsWithVariants.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(8.0),
        child: Text('没有找到模型变体。在模型列表中添加变体以启用此功能。'),
      );
    }

    for (final model in modelsWithVariants) {
      items.add(
        ExpansionTile(
          title: Text(model.name),
          subtitle: Text('${model.modelVariants.length}个可用变体'),
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('当前使用:',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 4),
                  Text('默认模型: ${model.model}'),

                  // 当前大纲模型变体使用情况
                  // if (controller.isDualModelMode.value) ...[
                  //   const SizedBox(height: 8),
                  //   Text('大纲模型: ${controller.outlineModelId.value.isEmpty ? model.name : controller.outlineModelId.value}'),
                  //   if (controller.outlineModelVariant.value.isNotEmpty)
                  //     Text('大纲变体: ${controller.outlineModelVariant.value}'),
                  // ],

                  // 当前章节模型变体使用情况
                  // if (controller.isDualModelMode.value) ...[
                  //   const SizedBox(height: 8),
                  //   Text('章节模型: ${controller.chapterModelId.value.isEmpty ? model.name : controller.chapterModelId.value}'),
                  //   if (controller.chapterModelVariant.value.isNotEmpty)
                  //     Text('章节变体: ${controller.chapterModelVariant.value}'),
                  // ],
                ],
              ),
            ),
          ],
        ),
      );

      items.add(const Divider());
    }

    return Column(children: items);
  }

  /// 构建Apply模型配置卡片
  Widget _buildApplyModelConfigCard(BuildContext context, ApiConfigController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.auto_fix_high, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Apply模型配置',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Tooltip(
                  message: 'Apply模型用于精确应用编辑建议，类似Cursor IDE的Apply机制',
                  child: Icon(Icons.help_outline, color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '专门用于精确应用编辑建议的轻量级模型，确保文本修改的准确性和一致性',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),

            // Apply模型选择
            Obx(() {
              final availableApplyModels = controller.getAvailableApplyModels();
              final currentApplyModel = controller.applyModel.value;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '选择Apply模型',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),

                  if (availableApplyModels.isEmpty) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.1),
                        border: Border.all(color: Colors.orange.withOpacity(0.3)),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.warning, color: Colors.orange[700]),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: Text(
                              '没有可用的模型。请先在"模型列表"标签页中配置模型',
                              style: TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ] else ...[
                    DropdownButtonFormField<String>(
                      value: currentApplyModel?.name,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: '选择Apply模型',
                      ),
                      items: [
                        const DropdownMenuItem<String>(
                          value: null,
                          child: Text('不使用Apply模型（使用传统方法）'),
                        ),
                        ...availableApplyModels.map((model) {
                          final recommendation = controller.getApplyModelRecommendation(model);
                          return DropdownMenuItem<String>(
                            value: model.name,
                            child: Container(
                              constraints: const BoxConstraints(maxWidth: 400),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // 推荐等级图标
                                  _getRecommendationIcon(recommendation),
                                  const SizedBox(width: 8),
                                  Flexible(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Flexible(child: Text(model.name, overflow: TextOverflow.ellipsis)),
                                            if (recommendation == ApplyModelRecommendation.recommended) ...[
                                              const SizedBox(width: 4),
                                              Container(
                                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                                decoration: BoxDecoration(
                                                  color: Colors.green.withOpacity(0.2),
                                                  borderRadius: BorderRadius.circular(10),
                                                ),
                                                child: Text(
                                                  '推荐',
                                                  style: TextStyle(
                                                    fontSize: 10,
                                                    color: Colors.green[700],
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ],
                                        ),
                                        Text(
                                          '${model.model} • ${_getRecommendationText(recommendation)}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey[600],
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                      ],
                      onChanged: (String? value) async {
                        if (value == null) {
                          controller.applyModel.value = null;
                          await controller.saveApplyModelConfig();
                        } else {
                          final selectedModel = availableApplyModels.firstWhere(
                            (model) => model.name == value,
                          );
                          await controller.setApplyModel(selectedModel);
                        }
                      },
                    ),

                    if (currentApplyModel != null) ...[
                      const SizedBox(height: 12),
                      Builder(
                        builder: (context) {
                          final recommendation = controller.getApplyModelRecommendation(currentApplyModel);
                          final optimizedConfig = controller.getOptimizedApplyModelConfig(currentApplyModel);

                          Color containerColor;
                          Color borderColor;
                          Color iconColor;
                          IconData iconData;

                          switch (recommendation) {
                            case ApplyModelRecommendation.recommended:
                              containerColor = Colors.green.withOpacity(0.1);
                              borderColor = Colors.green.withOpacity(0.3);
                              iconColor = Colors.green[700]!;
                              iconData = Icons.star;
                              break;
                            case ApplyModelRecommendation.acceptable:
                              containerColor = Colors.orange.withOpacity(0.1);
                              borderColor = Colors.orange.withOpacity(0.3);
                              iconColor = Colors.orange[700]!;
                              iconData = Icons.check_circle_outline;
                              break;
                            case ApplyModelRecommendation.notRecommended:
                              containerColor = Colors.red.withOpacity(0.1);
                              borderColor = Colors.red.withOpacity(0.3);
                              iconColor = Colors.red[700]!;
                              iconData = Icons.warning_outlined;
                              break;
                          }

                          return Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: containerColor,
                              border: Border.all(color: borderColor),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(iconData, color: iconColor, size: 16),
                                    const SizedBox(width: 4),
                                    Expanded(
                                      child: Text(
                                        '当前Apply模型: ${currentApplyModel.name}',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          color: iconColor,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: iconColor.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Text(
                                        _getRecommendationText(recommendation),
                                        style: TextStyle(
                                          fontSize: 10,
                                          color: iconColor,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  '基础模型: ${currentApplyModel.model}',
                                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                                ),
                                Text(
                                  'Apply配置: 温度 ${optimizedConfig.temperature} | Token ${optimizedConfig.maxTokens} | TopP ${optimizedConfig.topP}',
                                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                                ),
                                if (recommendation == ApplyModelRecommendation.notRecommended) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    '提示: 大型模型可能响应较慢，建议选择轻量级模型以获得更好的Apply体验',
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: Colors.red[600],
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  ],
                ],
              );
            }),

            const SizedBox(height: 16),

            // Apply模型说明
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue[700], size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'Apply模型工作原理',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• 主模型：负责理解用户意图、生成创作内容和编辑建议\n'
                    '• Apply模型：专门负责将编辑建议精确应用到文本内容\n'
                    '• 双模型协作：确保创作质量和应用准确性的完美平衡\n'
                    '• 自动回退：如果Apply模型失败，会自动使用传统方法',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取推荐等级图标
  Widget _getRecommendationIcon(ApplyModelRecommendation recommendation) {
    switch (recommendation) {
      case ApplyModelRecommendation.recommended:
        return Icon(Icons.star, color: Colors.green[600], size: 16);
      case ApplyModelRecommendation.acceptable:
        return Icon(Icons.check_circle_outline, color: Colors.orange[600], size: 16);
      case ApplyModelRecommendation.notRecommended:
        return Icon(Icons.warning_outlined, color: Colors.red[600], size: 16);
    }
  }

  /// 获取推荐等级文本
  String _getRecommendationText(ApplyModelRecommendation recommendation) {
    switch (recommendation) {
      case ApplyModelRecommendation.recommended:
        return '快速响应，推荐使用';
      case ApplyModelRecommendation.acceptable:
        return '性能适中，可以使用';
      case ApplyModelRecommendation.notRecommended:
        return '大型模型，可能较慢';
    }
  }
}
